import { DBOS } from "@dbos-inc/dbos-sdk";
import { ComplianceDocument, ComplianceViolation, ComplianceReport, KYCProfile } from '../types';
import { ComplianceDatabase } from '../database';
import { DocumentProcessing } from './document-processing';
import { KYCProcessing } from './kyc-processing';
import { ReportGeneration } from './report-generation';

export class ComplianceSystem {
  
  // Workflow Orchestration
  @DBOS.workflow()
  static async processComplianceDocument(document: ComplianceDocument): Promise<{
    status: string;
    violations: ComplianceViolation[];
  }> {
    DBOS.logger.info(`Starting compliance processing for document ${document.id}`);

    // Emit processing status
    await DBOS.setEvent('processing_status', 'started');

    // Save document to database
    await ComplianceDatabase.saveDocument(document);

    // Step 1: Validate document
    const isValid = await DocumentProcessing.validateDocument(document);
    if (!isValid) {
      await DBOS.setEvent('processing_status', 'failed_validation');
      // Update document status in database
      document.status = 'requires_review';
      await ComplianceDatabase.saveDocument(document);
      return { status: 'invalid', violations: [] };
    }

    await DBOS.setEvent('processing_status', 'validation_passed');

    // Step 2: Scan for violations
    const violations = await DocumentProcessing.scanForViolations(document);

    await DBOS.setEvent('violations_found', violations.length);

    // Step 3: Notify compliance team if violations found
    if (violations.length > 0) {
      await DocumentProcessing.notifyComplianceTeam(violations);
      await DBOS.setEvent('processing_status', 'violations_reported');
    }

    await DBOS.setEvent('processing_status', 'completed');

    const status = violations.length > 0 ? 'non_compliant' : 'compliant';

    // Update document status in database
    document.status = status as 'compliant' | 'non_compliant';
    await ComplianceDatabase.saveDocument(document);

    DBOS.logger.info(`Compliance processing completed for document ${document.id}: ${status}`);

    return { status, violations };
  }

  @DBOS.workflow()
  static async processKYCCustomer(profile: KYCProfile): Promise<{
    status: 'approved' | 'rejected' | 'under_review';
    riskScore: number;
    reasons: string[];
  }> {
    DBOS.logger.info(`Starting KYC processing for customer ${profile.customerId}`);

    await DBOS.setEvent('kyc_status', 'identity_verification');

    // Save initial KYC profile to database
    profile.status = 'pending';
    await ComplianceDatabase.saveKYCProfile(profile);

    // Step 1: Identity verification
    const identityResult = await KYCProcessing.verifyIdentity(profile);

    if (!identityResult.verified) {
      await DBOS.setEvent('kyc_status', 'identity_failed');
      // Update profile status in database
      profile.status = 'rejected';
      profile.riskScore = 100;
      await ComplianceDatabase.saveKYCProfile(profile);
      return {
        status: 'rejected',
        riskScore: 100,
        reasons: ['Identity verification failed']
      };
    }

    await DBOS.setEvent('kyc_status', 'risk_assessment');

    // Step 2: Risk assessment
    const riskScore = await KYCProcessing.performRiskAssessment(profile);
    profile.riskScore = riskScore;

    await DBOS.setEvent('kyc_status', 'sanctions_check');

    // Step 3: Sanctions list check
    const sanctionsResult = await KYCProcessing.checkSanctionsList(profile);

    if (sanctionsResult.isListed) {
      await DBOS.setEvent('kyc_status', 'sanctions_match');
      // Update profile status in database
      profile.status = 'rejected';
      profile.riskScore = 100;
      await ComplianceDatabase.saveKYCProfile(profile);
      return {
        status: 'rejected',
        riskScore: 100,
        reasons: [`Sanctions list match: ${sanctionsResult.details}`]
      };
    }

    // Determine final status
    let status: 'approved' | 'rejected' | 'under_review';
    const reasons: string[] = [];

    if (riskScore >= 70) {
      status = 'under_review';
      reasons.push('High risk score requires manual review');
    } else if (riskScore >= 50) {
      status = 'under_review';
      reasons.push('Medium risk score requires additional verification');
    } else {
      status = 'approved';
      reasons.push('Low risk profile - automatically approved');
    }

    // Update final profile status in database
    profile.status = status;
    profile.lastUpdated = new Date();
    await ComplianceDatabase.saveKYCProfile(profile);

    await DBOS.setEvent('kyc_status', 'completed');
    await DBOS.setEvent('final_status', status);

    DBOS.logger.info(`KYC processing completed for customer ${profile.customerId}: ${status}`);

    return { status, riskScore, reasons };
  }

  @DBOS.workflow()
  static async generateComplianceReport(
    timePeriod: 'monthly' | 'quarterly' | 'annual',
    reportType?: string,
    dateRange?: { startDate: string; endDate: string },
    standards?: string[]
  ): Promise<ComplianceReport> {
    DBOS.logger.info(`Generating ${timePeriod} compliance report of type ${reportType || 'default'}`);

    await DBOS.setEvent('report_status', 'metrics_generation');

    // Step 1: Generate metrics (with optional filtering by standards)
    const metrics = await ReportGeneration.generateComplianceMetrics(standards);

    await DBOS.setEvent('report_status', 'regulatory_updates');

    // Step 2: Fetch regulatory updates
    const regulatoryUpdates = await ReportGeneration.fetchRegulatoryUpdates();

    await DBOS.setEvent('report_status', 'impact_analysis');

    // Step 3: Analyze impact
    const recommendations = await ReportGeneration.analyzeRegulatoryImpact(regulatoryUpdates);

    await DBOS.setEvent('report_status', 'formatting');

    // Step 4: Format report with enhanced metadata
    const report = await ReportGeneration.formatComplianceReport(
      metrics,
      [], // Mock violations for report
      recommendations,
      timePeriod,
      reportType,
      dateRange
    );

    await DBOS.setEvent('report_status', 'completed');
    await DBOS.setEvent('report_id', report.id);
    await DBOS.setEvent('report_type', reportType || 'default');

    DBOS.logger.info(`Compliance report ${report.id} of type ${reportType || 'default'} generated successfully`);

    return report;
  }

  @DBOS.workflow()
  static async analyzeRegulatoryImpact(data: {
    regulatoryUpdates: string[];
    documentIds?: string[];
    timeframe: string;
  }): Promise<{
    workflowId: string;
    analysis: {
      impactLevel: string;
      recommendations: string[];
      affectedAreas: string[];
    };
  }> {
    const workflowId = DBOS.workflowID || `IMPACT-${Date.now()}`;
    DBOS.logger.info(`Starting regulatory impact analysis workflow ${workflowId} for ${data.regulatoryUpdates.length} updates`);

    await DBOS.setEvent('analysis_status', 'fetching_updates');

    // Step 1: Fetch regulatory updates from database (direct transaction call)
    const updates = await ComplianceDatabase.getRegulatoryUpdates();

    // Filter updates based on the requested ones
    const relevantUpdates = updates.filter(update =>
      data.regulatoryUpdates.some(reqUpdate =>
        update.title.toLowerCase().includes(reqUpdate.toLowerCase()) ||
        update.standard.toLowerCase().includes(reqUpdate.toLowerCase())
      )
    );

    await DBOS.setEvent('analysis_status', 'analyzing_impact');

    // Step 2: Analyze regulatory impact
    const recommendations = await ReportGeneration.analyzeRegulatoryImpact(relevantUpdates);

    await DBOS.setEvent('analysis_status', 'determining_impact_level');

    // Step 3: Determine overall impact level
    const highImpactCount = relevantUpdates.filter(u => u.impact === 'high').length;
    const mediumImpactCount = relevantUpdates.filter(u => u.impact === 'medium').length;

    let impactLevel = 'low';
    if (highImpactCount > 0) {
      impactLevel = 'high';
    } else if (mediumImpactCount > 0) {
      impactLevel = 'medium';
    }

    // Step 4: Identify affected areas
    const affectedAreas = [...new Set(relevantUpdates.map(u => u.standard))];

    await DBOS.setEvent('analysis_status', 'completed');

    const result = {
      workflowId,
      analysis: {
        impactLevel,
        recommendations,
        affectedAreas
      }
    };

    DBOS.logger.info(`Regulatory impact analysis completed for workflow ${workflowId}`);
    return result;
  }

  @DBOS.scheduled({ crontab: "0 9 * * 1" }) // Every Monday at 9 AM
  @DBOS.workflow()
  static async weeklyRegulatoryMonitoring(scheduledTime: Date, _startTime: Date): Promise<void> {
    DBOS.logger.info(`Starting weekly regulatory monitoring at ${scheduledTime}`);

    // Fetch and analyze regulatory updates
    const updates = await ReportGeneration.fetchRegulatoryUpdates();
    const recommendations = await ReportGeneration.analyzeRegulatoryImpact(updates);

    // Emit findings for monitoring
    await DBOS.setEvent('weekly_updates_count', updates.length);
    await DBOS.setEvent('weekly_recommendations', recommendations);

    DBOS.logger.info(`Weekly regulatory monitoring completed - ${updates.length} updates processed`);
  }
}
